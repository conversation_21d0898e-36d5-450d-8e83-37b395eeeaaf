'use client'

import { useEffect, useState } from 'react'

interface ViewCounterProps {
  slug: string
  className?: string
}

export default function ViewCounter({ slug, className = '' }: ViewCounterProps) {
  const [views, setViews] = useState<number>(0)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Get current views from localStorage
    const getViews = () => {
      try {
        const storedViews = localStorage.getItem('blog-views')
        const viewsData = storedViews ? JSON.parse(storedViews) : {}
        return viewsData[slug] || 0
      } catch (error) {
        console.error('Error reading views from localStorage:', error)
        return 0
      }
    }

    // Increment and store views
    const incrementViews = () => {
      try {
        const storedViews = localStorage.getItem('blog-views')
        const viewsData = storedViews ? JSON.parse(storedViews) : {}

        // Initialize with some seed data for demonstration if no data exists
        if (Object.keys(viewsData).length === 0) {
          viewsData['welcome-to-my-blog'] = 127
          viewsData['ai-automation-guide'] = 89
          viewsData['building-modern-web-apps'] = 156
        }

        // Check if this user has already viewed this post in this session
        const sessionKey = `viewed-${slug}`
        const hasViewedInSession = sessionStorage.getItem(sessionKey)

        if (!hasViewedInSession) {
          // Increment view count
          viewsData[slug] = (viewsData[slug] || 0) + 1
          localStorage.setItem('blog-views', JSON.stringify(viewsData))

          // Mark as viewed in this session to prevent multiple counts
          sessionStorage.setItem(sessionKey, 'true')
        }

        return viewsData[slug] || 0
      } catch (error) {
        console.error('Error updating views in localStorage:', error)
        return 0
      }
    }

    // Initialize views
    const currentViews = incrementViews()
    setViews(currentViews)
    setIsLoading(false)
  }, [slug])

  // Format views with commas for large numbers
  const formatViews = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`
    }
    return count.toLocaleString()
  }

  if (isLoading) {
    return (
      <span className={`text-primary-400 ${className}`}>
        <span className="inline-block w-8 h-4 bg-primary-200 rounded animate-pulse"></span> views
      </span>
    )
  }

  return (
    <span className={`text-primary-400 ${className}`}>
      {formatViews(views)} {views === 1 ? 'view' : 'views'}
    </span>
  )
}
