import type { Config } from 'tailwindcss'

const config: Config = {
    darkMode: ['class'],
    content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
  	extend: {
  		fontFamily: {
  			sans: [
  				'DM Sans',
  				'system-ui',
  				'sans-serif'
  			]
  		},
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			primary: {
  				'50': '#F0F2F2',
  				'100': '#C1D4D9',
  				'200': '#9BB9BF',
  				'300': '#7CA2A6',
  				'400': '#455759',
  				'500': '#455759',
  				'600': '#3A4B4D',
  				'700': '#2F3E40',
  				'800': '#243133',
  				'900': '#1A2426',
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			accent: {
  				'50': '#F0F2F2',
  				'100': '#C1D4D9',
  				'200': '#9BB9BF',
  				'300': '#7CA2A6',
  				'400': '#455759',
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			neutral: {
  				'50': '#F9FAFA',
  				'100': '#F0F2F2',
  				'200': '#E5E8E9',
  				'300': '#C1D4D9',
  				'400': '#9BB9BF',
  				'500': '#7CA2A6',
  				'600': '#455759',
  				'700': '#3A4B4D',
  				'800': '#2F3E40',
  				'900': '#1A2426'
  			},
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		animation: {
  			'fade-in': 'fadeIn 0.5s ease-in-out',
  			'slide-up': 'slideUp 0.6s ease-out',
  			wave: 'wave 2s ease-in-out infinite',
  			'marquee-ne': 'marqueeNE 1.5s ease-in-out infinite'
  		},
  		keyframes: {
  			fadeIn: {
  				'0%': {
  					opacity: '0'
  				},
  				'100%': {
  					opacity: '1'
  				}
  			},
  			slideUp: {
  				'0%': {
  					transform: 'translateY(20px)',
  					opacity: '0'
  				},
  				'100%': {
  					transform: 'translateY(0)',
  					opacity: '1'
  				}
  			},
  			wave: {
  				'0%, 100%': {
  					transform: 'scaleX(1)'
  				},
  				'50%': {
  					transform: 'scaleX(1.05)'
  				}
  			},
  			marqueeNE: {
  				'0%': {
  					transform: 'translate(0, 0)'
  				},
  				'50%': {
  					transform: 'translate(4px, -4px)'
  				},
  				'100%': {
  					transform: 'translate(0, 0)'
  				}
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		backgroundImage: {
  			'grid-pattern': 'url("data:image/svg+xml,%3csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3e%3cg fill=\'none\' fill-rule=\'evenodd\'%3e%3cg fill=\'%239C92AC\' fill-opacity=\'0.4\'%3e%3ccircle cx=\'30\' cy=\'30\' r=\'1.5\'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e")',
  			'grid-pattern-light': 'url("data:image/svg+xml,%3csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3e%3cg fill=\'none\' fill-rule=\'evenodd\'%3e%3cg fill=\'%23E5E7EB\' fill-opacity=\'0.4\'%3e%3ccircle cx=\'30\' cy=\'30\' r=\'1.5\'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e")'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
}
export default config
