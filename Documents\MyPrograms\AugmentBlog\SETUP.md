# Setup Guide for <PERSON>'s Blog Website

## Prerequisites

Before you can run this website, you need to install Node.js and npm on your system.

### Installing Node.js

1. **Download Node.js**:
   - Go to [https://nodejs.org/](https://nodejs.org/)
   - Download the LTS (Long Term Support) version
   - Choose the Windows installer (.msi file)

2. **Install Node.js**:
   - Run the downloaded installer
   - Follow the installation wizard
   - Make sure to check "Add to PATH" during installation

3. **Verify Installation**:
   Open Command Prompt or PowerShell and run:
   ```bash
   node --version
   npm --version
   ```
   You should see version numbers for both commands.

## Running the Website

Once Node.js is installed, follow these steps:

### 1. Install Dependencies

Open Command Prompt or PowerShell in the project directory and run:

```bash
npm install
```

This will install all the required packages listed in `package.json`.

### 2. Start Development Server

```bash
npm run dev
```

The website will be available at [http://localhost:3000](http://localhost:3000)

### 3. Build for Production

```bash
npm run build
npm start
```

## Adding Your Content

### Replace Placeholder Images

1. **Avatar Image**:
   - Replace `public/images/avatar.jpg` with your actual photo
   - Recommended size: 200x200px, square format

2. **Blog Images**:
   - Add your blog featured images to `public/images/blog/`
   - Update the `featuredImage` path in your blog post markdown files

3. **Project Images**:
   - Add your project images to `public/images/projects/`
   - Update the image paths in your project markdown files

### Create Your First Blog Post

1. Copy `content/blog/template.md` to create a new post
2. Rename it to `your-post-title.md` (use lowercase and hyphens)
3. Update the frontmatter (title, excerpt, date, etc.)
4. Write your content in Markdown
5. Save the file - it will automatically appear on your blog

### Create Your First Project

1. Copy `content/projects/template.md` to create a new project
2. Rename it to `your-project-name.md`
3. Update the frontmatter with project details
4. Add project images to `public/images/projects/`
5. Write your project description and case study
6. Save the file - it will automatically appear in your portfolio

## Email Configuration

To enable the contact forms, you'll need to set up email functionality:

### Option 1: EmailJS (Recommended for beginners)

1. Sign up at [https://www.emailjs.com/](https://www.emailjs.com/)
2. Create an email service and template
3. Update `src/lib/email.ts` with your EmailJS configuration
4. Replace the console.log statements with actual EmailJS calls

### Option 2: Backend API

For production use, consider setting up a backend API to handle email sending securely.

## Customization

### Update Personal Information

1. **Site Metadata**: Edit `src/app/layout.tsx`
2. **Header**: Update your name in `src/components/Header.tsx`
3. **Welcome Post**: Edit `content/blog/welcome-to-my-blog.md`

### Styling

- **Colors**: Modify `tailwind.config.ts`
- **Global Styles**: Edit `src/app/globals.css`
- **Component Styles**: Update individual component files

## Deployment

### Deploy to Vercel (Free & Easy)

1. Push your code to GitHub
2. Go to [https://vercel.com/](https://vercel.com/)
3. Sign up and connect your GitHub repository
4. Deploy with one click

### Deploy to Netlify

1. Push your code to GitHub
2. Go to [https://netlify.com/](https://netlify.com/)
3. Connect your repository and deploy

### Custom Domain

Once deployed, you can connect your `ernestromelo.com` domain:

1. In your hosting platform (Vercel/Netlify), go to domain settings
2. Add your custom domain
3. Update your domain's DNS settings to point to the hosting platform
4. Wait for DNS propagation (can take up to 24 hours)

## Troubleshooting

### Common Issues

1. **"npm is not recognized"**: Node.js is not installed or not in PATH
2. **Port 3000 already in use**: Another application is using the port
   - Try: `npm run dev -- --port 3001`
3. **Images not loading**: Check file paths and ensure images exist
4. **Build errors**: Check for TypeScript errors in the terminal

### Getting Help

If you encounter issues:
1. Check the terminal for error messages
2. Ensure all file paths are correct
3. Verify that all required images exist
4. Check that markdown frontmatter is properly formatted

## Next Steps

1. Install Node.js if not already installed
2. Run `npm install` to install dependencies
3. Replace placeholder images with your actual photos
4. Create your first blog post and project
5. Customize the styling to match your preferences
6. Set up email functionality
7. Deploy to your hosting platform
8. Connect your custom domain

Your modern, professional blog and portfolio website is ready to go!
