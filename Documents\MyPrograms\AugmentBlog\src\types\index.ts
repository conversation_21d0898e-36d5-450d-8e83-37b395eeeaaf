export interface BlogPost {
  slug: string;
  title: string;
  excerpt: string;
  date: string;
  featuredImage: string;
  content: string;
  readTime: number;
  tags?: string[];
  author?: string;
  categories?: string[];
  views?: number;
}

export interface Project {
  slug: string;
  title: string;
  description: string;
  featuredImage: string;
  images: string[];
  technologies: string[];
  liveUrl?: string;
  githubUrl?: string;
  content: string;
  date: string;
  category: string;
  client?: string;
  industry?: string;
  challenge?: string;
  strategy?: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
  consent: boolean;
}

export interface ProjectInquiryData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  website?: string;
  title: string;
  service: string;
  description: string;
  budget: string;
  deadline: string;
  hearAbout: string;
}

export interface TableOfContentsItem {
  id: string;
  title: string;
  level: number;
}

export interface NewsletterSubscription {
  email: string;
  firstName?: string;
  lastName?: string;
}

export interface NewsletterResponse {
  success: boolean;
  message: string;
  data?: any;
}
