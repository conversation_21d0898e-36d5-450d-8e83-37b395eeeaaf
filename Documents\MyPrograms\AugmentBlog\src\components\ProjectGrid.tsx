'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { Project } from '@/types'

interface ProjectGridProps {
  projects: Project[]
}

export default function ProjectGrid({ projects }: ProjectGridProps) {
  if (projects.length === 0) {
    return (
      <div className="text-center py-16">
        <h2 className="text-2xl font-semibold text-gray-600 mb-4">No projects yet</h2>
        <p className="text-gray-500">Check back soon for new projects!</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {projects.map((project, index) => (
        <motion.article
          key={project.slug}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className="group h-full"
        >
          <Link href={`/projects/${project.slug}`}>
            <div className="bg-white overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100 h-full flex flex-col">
              {/* Featured Image */}
              <div className="relative h-80 overflow-hidden">
                <Image
                  src={project.featuredImage}
                  alt={project.title}
                  width={600}
                  height={320}
                  className="object-cover object-center group-hover:scale-105 transition-transform duration-500 w-full h-full"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>

              {/* Content */}
              <div className="p-8 flex-1 flex flex-col">
                {/* Title */}
                <h2 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-gray-700 transition-colors duration-300">
                  {project.title}
                </h2>

                {/* Technologies/Services */}
                {project.technologies && project.technologies.length > 0 && (
                  <div className="text-sm text-gray-600 mt-auto">
                    {project.technologies.slice(0, 4).map((tech, techIndex) => (
                      <span key={tech}>
                        {tech}
                        {techIndex < Math.min(project.technologies.length, 4) - 1 && ' / '}
                      </span>
                    ))}
                    {project.technologies.length > 4 && (
                      <span> / +{project.technologies.length - 4} more</span>
                    )}
                  </div>
                )}
              </div>
            </div>
          </Link>
        </motion.article>
      ))}
    </div>
  )
}
