@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom placeholder styles */
.placeholder-sm::placeholder {
  font-size: 0.875rem; /* 14px */
}

.placeholder-xs::placeholder {
  font-size: 0.8125rem; /* 13px - slightly larger than before */
}

/* Date input specific styles */
input[type="date"].placeholder-xs::-webkit-datetime-edit-text,
input[type="date"].placeholder-xs::-webkit-datetime-edit-month-field,
input[type="date"].placeholder-xs::-webkit-datetime-edit-day-field,
input[type="date"].placeholder-xs::-webkit-datetime-edit-year-field {
  font-size: 0.8125rem !important; /* 13px */
}

input[type="date"].placeholder-xs::-webkit-datetime-edit-fields-wrapper {
  font-size: 0.8125rem !important; /* 13px */
}

input[type="date"].placeholder-xs::-webkit-input-placeholder {
  font-size: 0.8125rem !important; /* 13px */
}

/* Select dropdown styles */
.select-xs {
  font-size: 0.875rem; /* 14px for main text */
}

.select-xs option {
  font-size: 0.8125rem !important; /* 13px for options */
}

/* Specific styling for the default option */
.select-xs option[value=""] {
  font-size: 0.8125rem !important; /* 13px */
  color: #6b7280; /* gray-500 */
}

/* Force smaller text in select when no option is selected */
.select-xs:invalid {
  font-size: 0.8125rem !important; /* 13px */
  color: #6b7280; /* gray-500 */
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Custom scrollbar for TOC */
.toc-container::-webkit-scrollbar {
  width: 4px;
}

.toc-container::-webkit-scrollbar-track {
  background: #F0F2F2;
}

.toc-container::-webkit-scrollbar-thumb {
  background: #C1D4D9;
  border-radius: 2px;
}

.toc-container::-webkit-scrollbar-thumb:hover {
  background: #9BB9BF;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
  background: white; /* Fallback background for pages without Aurora */
}

/* Blog content styling */
.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4 {
  scroll-margin-top: 100px;
}

.blog-content h2 {
  @apply text-2xl font-bold mt-8 mb-4 text-gray-900;
}

.blog-content h3 {
  @apply text-xl font-semibold mt-6 mb-3 text-gray-800;
}

.blog-content h4 {
  @apply text-lg font-medium mt-4 mb-2 text-gray-700;
}

.blog-content p {
  @apply mb-4 leading-relaxed text-gray-800;
}

.blog-content ul,
.blog-content ol {
  @apply mb-4 pl-6 text-gray-800;
}

.blog-content li {
  @apply mb-2 text-gray-800;
}

.blog-content blockquote {
  @apply border-l-4 border-primary-500 pl-4 italic my-4 text-gray-600;
}

.blog-content code {
  @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono text-gray-800;
}

.blog-content pre {
  @apply bg-gray-100 p-4 rounded-lg overflow-x-auto my-4;
}

.blog-content pre code {
  @apply bg-transparent p-0 text-gray-800;
}

.blog-content strong {
  @apply font-bold text-gray-900;
}

.blog-content em {
  @apply italic text-gray-800;
}

.blog-content a {
  @apply text-primary-600 hover:text-primary-800 underline transition-colors duration-200;
}

.blog-content img {
  @apply rounded-lg shadow-md my-6;
}

/* Project content styling */
.project-content h1,
.project-content h2,
.project-content h3,
.project-content h4 {
  scroll-margin-top: 100px;
}

.project-content h2 {
  @apply text-2xl font-bold mt-8 mb-4 text-gray-900;
}

.project-content h3 {
  @apply text-xl font-semibold mt-6 mb-3 text-gray-900;
}

.project-content h4 {
  @apply text-lg font-medium mt-4 mb-2 text-gray-900;
}

.project-content p {
  @apply mb-4 leading-relaxed text-gray-900;
}

.project-content ul,
.project-content ol {
  @apply mb-4 pl-6 text-gray-900;
}

.project-content li {
  @apply mb-2 text-gray-900;
}

.project-content blockquote {
  @apply border-l-4 border-primary-500 pl-4 italic my-4 text-gray-700;
}

.project-content code {
  @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono text-gray-900;
}

.project-content pre {
  @apply bg-gray-100 p-4 rounded-lg overflow-x-auto my-4;
}

.project-content img {
  @apply my-6 w-full h-auto;
}

/* Force all text in project content to be dark */
.project-content * {
  color: #111827 !important;
}

/* Marquee animation for northeast direction */
@keyframes marquee-ne {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(4px, -4px);
  }
  50% {
    transform: translate(8px, -8px);
  }
  75% {
    transform: translate(4px, -4px);
  }
  100% {
    transform: translate(0, 0);
  }
}

.animate-marquee-ne {
  animation: marquee-ne 1s ease-in-out infinite;
}



@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --white: #ffffff;
    --black: #000000;
    --transparent: transparent;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
