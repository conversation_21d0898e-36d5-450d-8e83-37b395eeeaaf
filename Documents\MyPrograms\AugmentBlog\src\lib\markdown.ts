import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { remark } from 'remark'
import html from 'remark-html'
import { BlogPost, Project } from '@/types'

const postsDirectory = path.join(process.cwd(), 'content/blog')
const projectsDirectory = path.join(process.cwd(), 'content/projects')

export async function getBlogPosts(): Promise<BlogPost[]> {
  try {
    if (!fs.existsSync(postsDirectory)) {
      return []
    }

    const fileNames = fs.readdirSync(postsDirectory)
    const allPostsData = await Promise.all(
      fileNames
        .filter(fileName => fileName.endsWith('.md'))
        .map(async (fileName) => {
          const slug = fileName.replace(/\.md$/, '')
          const fullPath = path.join(postsDirectory, fileName)
          const fileContents = fs.readFileSync(fullPath, 'utf8')
          const { data, content } = matter(fileContents)

          // Process markdown content
          const processedContent = await remark()
            .use(html)
            .process(content)
          const contentHtml = processedContent.toString()

          // Calculate read time (rough estimate: 200 words per minute)
          const wordCount = content.split(/\s+/).length
          const readTime = Math.ceil(wordCount / 200)

          return {
            slug,
            title: data.title || 'Untitled',
            excerpt: data.excerpt || '',
            date: data.date || new Date().toISOString(),
            featuredImage: data.featuredImage || '/images/blog/default.png',
            content: contentHtml,
            readTime,
            tags: data.tags || [],
            author: data.author || 'Ernst',
            categories: data.categories || [],
          } as BlogPost
        })
    )

    return allPostsData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  } catch (error) {
    console.error('Error reading blog posts:', error)
    return []
  }
}

export async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    const fullPath = path.join(postsDirectory, `${slug}.md`)
    
    if (!fs.existsSync(fullPath)) {
      return null
    }

    const fileContents = fs.readFileSync(fullPath, 'utf8')
    const { data, content } = matter(fileContents)

    const processedContent = await remark()
      .use(html)
      .process(content)
    const contentHtml = processedContent.toString()

    const wordCount = content.split(/\s+/).length
    const readTime = Math.ceil(wordCount / 200)

    return {
      slug,
      title: data.title || 'Untitled',
      excerpt: data.excerpt || '',
      date: data.date || new Date().toISOString(),
      featuredImage: data.featuredImage || '/images/blog/default.png',
      content: contentHtml,
      readTime,
      tags: data.tags || [],
      author: data.author || 'Ernst',
      categories: data.categories || [],
    } as BlogPost
  } catch (error) {
    console.error('Error reading blog post:', error)
    return null
  }
}

export async function getProjects(): Promise<Project[]> {
  try {
    if (!fs.existsSync(projectsDirectory)) {
      return []
    }

    const fileNames = fs.readdirSync(projectsDirectory)
    const allProjectsData = await Promise.all(
      fileNames
        .filter(fileName => fileName.endsWith('.md'))
        .map(async (fileName) => {
          const slug = fileName.replace(/\.md$/, '')
          const fullPath = path.join(projectsDirectory, fileName)
          const fileContents = fs.readFileSync(fullPath, 'utf8')
          const { data, content } = matter(fileContents)

          const processedContent = await remark()
            .use(html)
            .process(content)
          const contentHtml = processedContent.toString()

          return {
            slug,
            title: data.title || 'Untitled',
            description: data.description || '',
            featuredImage: data.featuredImage || '/images/projects/default.jpg',
            images: data.images || [],
            technologies: data.technologies || [],
            liveUrl: data.liveUrl,
            githubUrl: data.githubUrl,
            content: contentHtml,
            date: data.date || new Date().toISOString(),
            category: data.category || 'Web Development',
            client: data.client,
            industry: data.industry,
            challenge: data.challenge,
            strategy: data.strategy,
          } as Project
        })
    )

    return allProjectsData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  } catch (error) {
    console.error('Error reading projects:', error)
    return []
  }
}

export async function getProject(slug: string): Promise<Project | null> {
  try {
    const fullPath = path.join(projectsDirectory, `${slug}.md`)

    if (!fs.existsSync(fullPath)) {
      return null
    }

    const fileContents = fs.readFileSync(fullPath, 'utf8')
    const { data, content } = matter(fileContents)

    const processedContent = await remark()
      .use(html)
      .process(content)
    const contentHtml = processedContent.toString()

    return {
      slug,
      title: data.title || 'Untitled',
      description: data.description || '',
      featuredImage: data.featuredImage || '/images/projects/default.jpg',
      images: data.images || [],
      technologies: data.technologies || [],
      liveUrl: data.liveUrl,
      githubUrl: data.githubUrl,
      content: contentHtml,
      date: data.date || new Date().toISOString(),
      category: data.category || 'Web Development',
      client: data.client,
      industry: data.industry,
      challenge: data.challenge,
      strategy: data.strategy,
    } as Project
  } catch (error) {
    console.error('Error reading project:', error)
    return null
  }
}

export async function getAdjacentPosts(currentSlug: string): Promise<{
  previousPost: BlogPost | null;
  nextPost: BlogPost | null;
}> {
  const allPosts = await getBlogPosts()
  const currentIndex = allPosts.findIndex(post => post.slug === currentSlug)

  return {
    previousPost: currentIndex > 0 ? allPosts[currentIndex - 1] : null,
    nextPost: currentIndex < allPosts.length - 1 ? allPosts[currentIndex + 1] : null,
  }
}
