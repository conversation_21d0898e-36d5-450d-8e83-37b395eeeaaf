---
title: "Personal Blog & Portfolio Website"
description: "A modern, minimalist blog and portfolio website built with Next.js 14, featuring markdown-based content management and responsive design."
date: "2024-01-15"
category: "Web Development"
featuredImage: "/images/projects/robot.jpg"
images:
  - "/images/projects/default.jpg"
  - "/images/projects/default.jpg"
  - "/images/projects/default.jpg"
technologies: ["Next.js 14", "TypeScript", "Tailwind CSS", "Framer Motion", "Markdown"]
liveUrl: "https://ernestromelo.com"
githubUrl: "https://github.com/ernestromelo/blog"
---

# Personal Blog & Portfolio Website

A clean, modern, and fully responsive personal website that serves as both a blog and portfolio showcase. Built with performance and user experience in mind.

## The Challenge

Multi-Purpose Platform
Content Management
Client Engagement

## The Solution

Next.js Application
Markdown Content
Balanced Functionality

## Key Features

- **Markdown-Based Content**: Easy content creation and management using markdown files
- **Responsive Design**: Optimized for all devices with a mobile-first approach
- **Auto-Generated Table of Contents**: Dynamic TOC for blog posts based on headings
- **Project Showcase**: Grid-based portfolio with detailed project pages
- **Contact Integration**: Smart contact forms with location-based currency detection
- **Performance Optimized**: Fast loading times with Next.js 14 optimizations

## Technical Implementation

### Architecture

The website follows a modern JAMstack architecture:
- **Frontend**: Next.js 14 with App Router
- **Styling**: Tailwind CSS for utility-first styling
- **Content**: File-based markdown content with frontmatter
- **Animations**: Framer Motion for smooth, subtle animations
- **Deployment**: Optimized for static generation and edge deployment

### Key Technologies

**Next.js 14**: Chosen for its excellent developer experience, built-in optimizations, and powerful App Router that enables file-based routing and layouts.

**TypeScript**: Provides type safety and better developer experience, especially important for content management and form handling.

**Tailwind CSS**: Enables rapid UI development with consistent design patterns and responsive utilities.

**Framer Motion**: Adds polished animations that enhance user experience without being distracting.

### Challenges & Solutions

**Content Management**: Instead of using a heavy CMS, I implemented a markdown-based system with frontmatter for metadata. This provides version control benefits and easy content creation.

**Table of Contents Generation**: Built a custom component that parses HTML content and generates a sticky TOC with smooth scrolling and active section highlighting.

**Form Handling**: Implemented smart forms that detect user location to auto-populate currency fields for project inquiries.

## Results & Impact

- **Performance**: Lighthouse score of 95+ across all metrics
- **User Experience**: Clean, intuitive navigation with subtle animations
- **Content Management**: Easy blog post creation using markdown templates
- **Client Engagement**: Streamlined project inquiry process with smart forms

## Future Improvements

- **Search Functionality**: Add full-text search across blog posts
- **Comment System**: Integrate a commenting system for blog posts
- **Analytics Dashboard**: Add visitor analytics and engagement metrics
- **Newsletter Integration**: Add email subscription for blog updates
- **Dark Mode**: Implement a dark theme option

---

This project demonstrates my ability to create modern, performant web applications while maintaining clean code architecture and excellent user experience. The markdown-based approach provides flexibility for content management while keeping the technical complexity manageable.
