'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'

export default function Header() {
  const [isContactModalOpen, setIsContactModalOpen] = useState(false)

  const handleContactClick = () => {
    setIsContactModalOpen(true)
    // Dispatch custom event to open contact modal
    window.dispatchEvent(new CustomEvent('openContactModal'))
  }

  return (
    <motion.header
      className="fixed top-0 left-0 right-0 backdrop-blur-md z-50"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo/Avatar Section */}
          <Link href="/" className="flex items-center group">
            <div className="relative w-10 h-10 rounded-full overflow-hidden ring-2 ring-neutral-300 group-hover:ring-primary-300 transition-all duration-300">
              <Image
                src="/images/avatar.jpg"
                alt="Ernst Romelo"
                width={40}
                height={40}
                className="object-cover rounded-full"
                priority
              />
            </div>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              href="/"
              className="text-primary-500 hover:text-primary-300 transition-colors duration-300 font-medium"
            >
              Blog
            </Link>
            <Link
              href="/projects"
              className="text-primary-500 hover:text-primary-300 transition-colors duration-300 font-medium"
            >
              Projects
            </Link>
            <button
              onClick={handleContactClick}
              className="px-4 py-2 bg-primary-50 hover:bg-primary-100 text-primary-600 hover:text-primary-700 transition-all duration-300 font-medium rounded-lg shadow-sm hover:shadow-md"
            >
              <EMAIL>
            </button>
          </nav>

          {/* Mobile contact button */}
          <div className="md:hidden">
            <button
              onClick={handleContactClick}
              className="px-3 py-2 bg-primary-50 hover:bg-primary-100 text-primary-600 hover:text-primary-700 transition-all duration-300 font-medium rounded-lg shadow-sm hover:shadow-md text-sm"
            >
              Contact
            </button>
          </div>
        </div>
      </div>
    </motion.header>
  )
}
