<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .animate-fade-in { animation: fadeIn 0.5s ease-in-out; }
        .animate-slide-up { animation: slideUp 0.6s ease-out; }
        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        @keyframes slideUp { from { transform: translateY(20px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
        .group:hover .group-hover\:scale-105 { transform: scale(1.05); }
        .group:hover .group-hover\:translate-x-1 { transform: translateX(0.25rem); }
        .transition-all { transition: all 0.3s ease; }
        .transition-transform { transition: transform 0.3s ease; }
        .transition-colors { transition: color 0.3s ease, background-color 0.3s ease; }
    </style>
</head>
<body class="bg-white">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 bg-white/80 backdrop-blur-md border-b border-gray-100 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo/Avatar Section -->
                <div class="flex items-center space-x-3 group cursor-pointer">
                    <div class="relative w-10 h-10 rounded-full overflow-hidden ring-2 ring-gray-200 group-hover:ring-blue-500 transition-all duration-300">
                        <div class="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold">
                            ER
                        </div>
                    </div>
                    <span class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                        Ernst Romelo
                    </span>
                </div>

                <!-- Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="#" class="text-blue-600 font-medium">Blog</a>
                    <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium">Projects</a>
                    <button class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium">Contact</button>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pt-20">
        <div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <!-- Hero Section -->
                <div class="text-center mb-16">
                    <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6 animate-fade-in">
                        Welcome to my Blog
                    </h1>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto animate-slide-up">
                        Thoughts on web development, AI automation, and the intersection of technology and creativity.
                    </p>
                </div>

                <!-- Blog Posts Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Blog Post 1 -->
                    <article class="group cursor-pointer">
                        <div class="bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 group-hover:border-blue-200">
                            <!-- Featured Image -->
                            <div class="relative h-48 overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600">
                                <div class="absolute inset-0 flex items-center justify-center text-white text-lg font-semibold">
                                    Featured Image
                                </div>
                                <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </div>

                            <!-- Content -->
                            <div class="p-6">
                                <!-- Date and Read Time -->
                                <div class="flex items-center text-sm text-gray-500 mb-3">
                                    <time>January 15, 2024</time>
                                    <span class="mx-2">•</span>
                                    <span>5 min read</span>
                                </div>

                                <!-- Title -->
                                <h2 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                                    Welcome to My Blog
                                </h2>

                                <!-- Excerpt -->
                                <p class="text-gray-600 mb-4">
                                    An introduction to my personal blog where I share insights on web development, AI automation, and the intersection of technology and creativity.
                                </p>

                                <!-- Tags -->
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <span class="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full">introduction</span>
                                    <span class="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full">web-development</span>
                                    <span class="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full">ai-automation</span>
                                </div>

                                <!-- Read More Arrow -->
                                <div class="flex items-center text-blue-600 font-medium group-hover:text-blue-700">
                                    <span class="text-sm">Read more</span>
                                    <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </article>

                    <!-- Blog Post 2 -->
                    <article class="group cursor-pointer">
                        <div class="bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 group-hover:border-blue-200">
                            <div class="relative h-48 overflow-hidden bg-gradient-to-br from-green-500 to-blue-600">
                                <div class="absolute inset-0 flex items-center justify-center text-white text-lg font-semibold">
                                    Featured Image
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center text-sm text-gray-500 mb-3">
                                    <time>January 10, 2024</time>
                                    <span class="mx-2">•</span>
                                    <span>8 min read</span>
                                </div>
                                <h2 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                                    Building Modern React Applications
                                </h2>
                                <p class="text-gray-600 mb-4">
                                    A comprehensive guide to building scalable React applications with Next.js 14, TypeScript, and modern development practices.
                                </p>
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <span class="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full">react</span>
                                    <span class="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full">nextjs</span>
                                    <span class="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full">typescript</span>
                                </div>
                                <div class="flex items-center text-blue-600 font-medium group-hover:text-blue-700">
                                    <span class="text-sm">Read more</span>
                                    <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </article>

                    <!-- Blog Post 3 -->
                    <article class="group cursor-pointer">
                        <div class="bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 group-hover:border-blue-200">
                            <div class="relative h-48 overflow-hidden bg-gradient-to-br from-purple-500 to-pink-600">
                                <div class="absolute inset-0 flex items-center justify-center text-white text-lg font-semibold">
                                    Featured Image
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center text-sm text-gray-500 mb-3">
                                    <time>January 5, 2024</time>
                                    <span class="mx-2">•</span>
                                    <span>6 min read</span>
                                </div>
                                <h2 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                                    AI Automation in Web Development
                                </h2>
                                <p class="text-gray-600 mb-4">
                                    Exploring how artificial intelligence can streamline development workflows and enhance user experiences in modern web applications.
                                </p>
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <span class="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full">ai</span>
                                    <span class="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full">automation</span>
                                    <span class="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full">workflow</span>
                                </div>
                                <div class="flex items-center text-blue-600 font-medium group-hover:text-blue-700">
                                    <span class="text-sm">Read more</span>
                                    <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </article>
                </div>

                <!-- Projects Section Preview -->
                <div class="mt-20 pt-16 border-t border-gray-200">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Projects</h2>
                        <p class="text-lg text-gray-600">A showcase of my recent work in web development and AI automation</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Project 1 -->
                        <article class="group cursor-pointer">
                            <div class="bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 group-hover:border-blue-200">
                                <div class="relative h-48 overflow-hidden bg-gradient-to-br from-indigo-500 to-purple-600">
                                    <div class="absolute inset-0 flex items-center justify-center text-white text-lg font-semibold">
                                        Project Screenshot
                                    </div>
                                    <div class="absolute top-4 left-4">
                                        <span class="px-3 py-1 bg-white/90 backdrop-blur-sm text-gray-700 text-xs font-medium rounded-full">
                                            Web Development
                                        </span>
                                    </div>
                                </div>
                                <div class="p-6">
                                    <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                                        Personal Blog & Portfolio Website
                                    </h3>
                                    <p class="text-gray-600 mb-4">
                                        A modern, minimalist blog and portfolio website built with Next.js 14, featuring markdown-based content management and responsive design.
                                    </p>
                                    <div class="flex flex-wrap gap-2 mb-4">
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">Next.js 14</span>
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">TypeScript</span>
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">Tailwind CSS</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">Live Demo</a>
                                            <a href="#" class="text-gray-600 hover:text-gray-700 text-sm">GitHub</a>
                                        </div>
                                        <div class="flex items-center text-blue-600 font-medium group-hover:text-blue-700">
                                            <span class="text-sm">View Project</span>
                                            <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>

                        <!-- Project 2 -->
                        <article class="group cursor-pointer">
                            <div class="bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 group-hover:border-blue-200">
                                <div class="relative h-48 overflow-hidden bg-gradient-to-br from-emerald-500 to-teal-600">
                                    <div class="absolute inset-0 flex items-center justify-center text-white text-lg font-semibold">
                                        Project Screenshot
                                    </div>
                                    <div class="absolute top-4 left-4">
                                        <span class="px-3 py-1 bg-white/90 backdrop-blur-sm text-gray-700 text-xs font-medium rounded-full">
                                            AI Automation
                                        </span>
                                    </div>
                                </div>
                                <div class="p-6">
                                    <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                                        AI-Powered Content Generator
                                    </h3>
                                    <p class="text-gray-600 mb-4">
                                        An intelligent content generation system that uses AI to create blog posts, social media content, and marketing materials automatically.
                                    </p>
                                    <div class="flex flex-wrap gap-2 mb-4">
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">OpenAI API</span>
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">Python</span>
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">React</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">Live Demo</a>
                                            <a href="#" class="text-gray-600 hover:text-gray-700 text-sm">GitHub</a>
                                        </div>
                                        <div class="flex items-center text-blue-600 font-medium group-hover:text-blue-700">
                                            <span class="text-sm">View Project</span>
                                            <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Contact Modal Trigger -->
    <div class="fixed bottom-8 right-8">
        <button onclick="showContactModal()" class="bg-blue-600 text-white px-6 py-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors duration-300 font-medium">
            Contact Me
        </button>
    </div>

    <!-- Contact Modal (Hidden by default) -->
    <div id="contactModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 hidden">
        <div class="bg-white rounded-xl shadow-2xl max-w-md w-full">
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <h2 class="text-2xl font-bold text-gray-900">Get in Touch</h2>
                <button onclick="hideContactModal()" class="text-gray-400 hover:text-gray-600 transition-colors duration-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <p class="text-gray-600 mb-6">Have a question or want to collaborate? I'd love to hear from you!</p>
                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                        <input type="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Message *</label>
                        <textarea rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Tell me about your project or question..."></textarea>
                    </div>
                    <div class="flex items-start">
                        <input type="checkbox" class="mt-1 mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label class="text-sm text-gray-600">I consent to having this website store my submitted information so Ernst can respond to my inquiry. *</label>
                    </div>
                    <button type="submit" class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-300">
                        Send Message
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function showContactModal() {
            document.getElementById('contactModal').classList.remove('hidden');
        }
        
        function hideContactModal() {
            document.getElementById('contactModal').classList.add('hidden');
        }
        
        // Close modal when clicking outside
        document.getElementById('contactModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideContactModal();
            }
        });
    </script>
</body>
</html>
