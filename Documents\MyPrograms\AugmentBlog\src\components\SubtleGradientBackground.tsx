'use client'

import { useEffect, useRef } from 'react'

export default function SubtleGradientBackground() {
  const backgroundRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!backgroundRef.current) return

      const { clientX, clientY } = e
      const { innerWidth, innerHeight } = window

      // Calculate mouse position as percentage
      const xPercent = (clientX / innerWidth) * 100
      const yPercent = (clientY / innerHeight) * 100

      // Create subtle gradient shifts based on mouse position
      // Using Norwegian landscape inspired colors: cool blues, grays, whites
      const gradient = `
        radial-gradient(
          circle at ${xPercent}% ${yPercent}%,
          rgba(219, 234, 254, 0.15) 0%,
          rgba(239, 246, 255, 0.1) 25%,
          rgba(248, 250, 252, 0.05) 50%,
          rgba(241, 245, 249, 0.08) 75%,
          rgba(226, 232, 240, 0.05) 100%
        ),
        linear-gradient(
          ${45 + (xPercent - 50) * 0.1}deg,
          rgba(219, 234, 254, 0.05) 0%,
          rgba(248, 250, 252, 0.03) 25%,
          rgba(255, 255, 255, 0.01) 50%,
          rgba(241, 245, 249, 0.04) 75%,
          rgba(226, 232, 240, 0.03) 100%
        )
      `

      backgroundRef.current.style.background = gradient
    }

    // Throttle mouse move events for better performance
    let ticking = false
    const throttledMouseMove = (e: MouseEvent) => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleMouseMove(e)
          ticking = false
        })
        ticking = true
      }
    }

    // Add event listener
    window.addEventListener('mousemove', throttledMouseMove)

    // Set initial gradient
    const initialGradient = `
      radial-gradient(
        circle at 50% 50%,
        rgba(219, 234, 254, 0.15) 0%,
        rgba(239, 246, 255, 0.1) 25%,
        rgba(248, 250, 252, 0.05) 50%,
        rgba(241, 245, 249, 0.08) 75%,
        rgba(226, 232, 240, 0.05) 100%
      ),
      linear-gradient(
        45deg,
        rgba(219, 234, 254, 0.05) 0%,
        rgba(248, 250, 252, 0.03) 25%,
        rgba(255, 255, 255, 0.01) 50%,
        rgba(241, 245, 249, 0.04) 75%,
        rgba(226, 232, 240, 0.03) 100%
      )
    `
    
    if (backgroundRef.current) {
      backgroundRef.current.style.background = initialGradient
    }

    // Cleanup
    return () => {
      window.removeEventListener('mousemove', throttledMouseMove)
    }
  }, [])

  return (
    <div
      ref={backgroundRef}
      className="fixed inset-0 -z-10 transition-all duration-300 ease-out"
      style={{
        background: `
          radial-gradient(
            circle at 50% 50%,
            rgba(219, 234, 254, 0.15) 0%,
            rgba(239, 246, 255, 0.1) 25%,
            rgba(248, 250, 252, 0.05) 50%,
            rgba(241, 245, 249, 0.08) 75%,
            rgba(226, 232, 240, 0.05) 100%
          ),
          linear-gradient(
            45deg,
            rgba(219, 234, 254, 0.05) 0%,
            rgba(248, 250, 252, 0.03) 25%,
            rgba(255, 255, 255, 0.01) 50%,
            rgba(241, 245, 249, 0.04) 75%,
            rgba(226, 232, 240, 0.03) 100%
          )
        `
      }}
    />
  )
}
